/**
 * UTM Parameter Tracking Utilities
 * Handles UTM parameter extraction, storage, and tracking for Google Analytics
 */

export interface UTMParameters {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
  utm_id?: string;
}

const UTM_STORAGE_KEY = 'bigriver_utm_params';
const UTM_EXPIRY_KEY = 'bigriver_utm_expiry';
const UTM_EXPIRY_HOURS = 24; // UTM parameters expire after 24 hours

/**
 * Extract UTM parameters from URL search params
 */
export function extractUTMFromURL(searchParams: URLSearchParams): UTMParameters {
  const utmParams: UTMParameters = {};
  
  // Extract all UTM parameters
  const utmKeys = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'utm_id'];
  
  utmKeys.forEach(key => {
    const value = searchParams.get(key);
    if (value) {
      utmParams[key as keyof UTMParameters] = value;
    }
  });
  
  return utmParams;
}

/**
 * Store UTM parameters in session storage with expiry
 */
export function storeUTMParameters(utmParams: UTMParameters): void {
  if (typeof window === 'undefined') return;
  
  try {
    // Only store if we have at least one UTM parameter
    if (Object.keys(utmParams).length > 0) {
      const expiryTime = Date.now() + (UTM_EXPIRY_HOURS * 60 * 60 * 1000);
      
      sessionStorage.setItem(UTM_STORAGE_KEY, JSON.stringify(utmParams));
      sessionStorage.setItem(UTM_EXPIRY_KEY, expiryTime.toString());
      
      console.log('UTM parameters stored:', utmParams);
    }
  } catch (error) {
    console.warn('Failed to store UTM parameters:', error);
  }
}

/**
 * Retrieve stored UTM parameters (if not expired)
 */
export function getStoredUTMParameters(): UTMParameters | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const storedParams = sessionStorage.getItem(UTM_STORAGE_KEY);
    const expiryTime = sessionStorage.getItem(UTM_EXPIRY_KEY);
    
    if (!storedParams || !expiryTime) return null;
    
    // Check if expired
    if (Date.now() > parseInt(expiryTime)) {
      clearStoredUTMParameters();
      return null;
    }
    
    return JSON.parse(storedParams);
  } catch (error) {
    console.warn('Failed to retrieve UTM parameters:', error);
    return null;
  }
}

/**
 * Clear stored UTM parameters
 */
export function clearStoredUTMParameters(): void {
  if (typeof window === 'undefined') return;
  
  try {
    sessionStorage.removeItem(UTM_STORAGE_KEY);
    sessionStorage.removeItem(UTM_EXPIRY_KEY);
  } catch (error) {
    console.warn('Failed to clear UTM parameters:', error);
  }
}

/**
 * Get current UTM parameters (from URL or storage)
 */
export function getCurrentUTMParameters(searchParams?: URLSearchParams): UTMParameters {
  // First try to get from URL
  if (searchParams) {
    const urlUTM = extractUTMFromURL(searchParams);
    if (Object.keys(urlUTM).length > 0) {
      // Store new UTM parameters
      storeUTMParameters(urlUTM);
      return urlUTM;
    }
  }
  
  // Fallback to stored parameters
  return getStoredUTMParameters() || {};
}

/**
 * Create UTM-enabled URL
 */
export function createUTMURL(baseUrl: string, utmParams: UTMParameters): string {
  const url = new URL(baseUrl, window.location.origin);
  
  Object.entries(utmParams).forEach(([key, value]) => {
    if (value) {
      url.searchParams.set(key, value);
    }
  });
  
  return url.toString();
}

/**
 * Common UTM parameter sets for different campaign types
 */
export const UTM_PRESETS = {
  email: {
    utm_medium: 'email',
  },
  social: {
    utm_medium: 'social',
  },
  paid_search: {
    utm_medium: 'cpc',
  },
  organic_search: {
    utm_medium: 'organic',
  },
  referral: {
    utm_medium: 'referral',
  },
  direct: {
    utm_medium: 'direct',
  },
} as const;

/**
 * Generate UTM parameters for common Big River Coffee campaigns
 */
export function generateCampaignUTM(
  source: string,
  campaign: string,
  medium: keyof typeof UTM_PRESETS = 'referral',
  content?: string
): UTMParameters {
  return {
    utm_source: source,
    utm_campaign: campaign,
    utm_content: content,
    ...UTM_PRESETS[medium],
  };
}

/**
 * Format UTM parameters for Google Analytics events
 */
export function formatUTMForAnalytics(utmParams: UTMParameters) {
  return {
    campaign_source: utmParams.utm_source,
    campaign_medium: utmParams.utm_medium,
    campaign_name: utmParams.utm_campaign,
    campaign_content: utmParams.utm_content,
    campaign_term: utmParams.utm_term,
    campaign_id: utmParams.utm_id,
  };
}
