import { useEffect } from 'react';

/**
 * Performance optimizations component
 * Implements various performance improvements for better Core Web Vitals
 */
export function PerformanceOptimizations() {
  useEffect(() => {
    // Preload critical images when the page loads
    const preloadCriticalImages = () => {
      const criticalImages = [
        '/headerlogo.svg',
        '/hpheronew.svg',
        '/mobilepopup.webp',
        '/brewedforwild.webp',
        '/brewedforwildmobile.webp'
      ];

      criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    };

    // Optimize images by adding loading attributes to existing images
    const optimizeExistingImages = () => {
      const images = document.querySelectorAll('img:not([loading])');
      images.forEach((img, index) => {
        // First few images should load eagerly, rest lazily
        if (index < 3) {
          img.setAttribute('loading', 'eager');
        } else {
          img.setAttribute('loading', 'lazy');
        }
      });
    };

    // Add intersection observer for lazy loading fallback
    const addIntersectionObserver = () => {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        });

        // Observe images with data-src attribute
        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }
    };

    // Optimize video loading
    const optimizeVideos = () => {
      const videos = document.querySelectorAll('video');
      videos.forEach(video => {
        // Add preload="metadata" for better performance
        if (!video.hasAttribute('preload')) {
          video.setAttribute('preload', 'metadata');
        }
        
        // Add loading="lazy" if supported
        if ('loading' in HTMLVideoElement.prototype) {
          video.setAttribute('loading', 'lazy');
        }
      });
    };

    // Defer non-critical JavaScript
    const deferNonCriticalJS = () => {
      // Add defer attribute to non-critical scripts
      const scripts = document.querySelectorAll('script[src]:not([defer]):not([async])');
      scripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src && !src.includes('gtag') && !src.includes('analytics')) {
          script.setAttribute('defer', '');
        }
      });
    };

    // Optimize font loading
    const optimizeFonts = () => {
      // Add font-display: swap to improve text rendering
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-display: swap;
        }
      `;
      document.head.appendChild(style);
    };

    // Run optimizations
    preloadCriticalImages();
    optimizeExistingImages();
    addIntersectionObserver();
    optimizeVideos();
    deferNonCriticalJS();
    optimizeFonts();

    // Cleanup function
    return () => {
      // Remove any event listeners if needed
    };
  }, []);

  return null; // This component doesn't render anything
}

/**
 * Critical Resource Preloader
 * Preloads the most important resources for faster page loads
 */
export function CriticalResourcePreloader() {
  useEffect(() => {
    // Preload critical CSS
    const preloadCSS = (href: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    };

    // Preload critical fonts (if any)
    const preloadFonts = () => {
      // Add any critical fonts here
      const criticalFonts = [
        // '/fonts/your-critical-font.woff2'
      ];

      criticalFonts.forEach(font => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = font;
        document.head.appendChild(link);
      });
    };

    preloadFonts();
  }, []);

  return null;
}

/**
 * Service Worker Registration
 * Registers a service worker for caching and offline functionality
 */
export function ServiceWorkerRegistration() {
  useEffect(() => {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  }, []);

  return null;
}
