import {Link} from 'react-router';
import {Image, Money} from '@shopify/hydrogen';
import type {
  ProductItemFragment,
  CollectionItemFragment,
  RecommendedProductFragment,
} from 'storefrontapi.generated';
import {useVariantUrl} from '~/lib/variants';
import {AddToCartButton} from '~/components/AddToCartButton';

export function ProductCard({
  product,
  loading,
  viewMode = 'grid',
}: {
  product:
    | CollectionItemFragment
    | ProductItemFragment
    | RecommendedProductFragment;
  loading?: 'eager' | 'lazy';
  viewMode?: 'grid' | 'list';
}) {
  const variantUrl = useVariantUrl(product.handle);
  const image = product.featuredImage;
  const firstVariant = 'variants' in product ? product.variants?.nodes?.[0] : null;

  // Function to determine badge type based on product title
  const getBadgeType = () => {
    if (product.title.toLowerCase().includes('dark')) {
      return 'Dark Roast';
    } else if (product.title.toLowerCase().includes('medium')) {
      return 'Medium Roast';
    } else if (product.title.toLowerCase().includes('light')) {
      return 'Light Roast';
    } else if (product.title.toLowerCase().includes('blend')) {
      return 'Signature Blend';
    } else if (product.title.toLowerCase().includes('single')) {
      return 'Single Origin';
    } else {
      return 'Premium';
    }
  };

  // Function to get badge color class
  const getBadgeColorClass = (badgeType: string) => {
    switch (badgeType) {
      case 'Dark Roast':
        return 'bg-amber-900 text-white';
      case 'Medium Roast':
        return 'bg-amber-600 text-white';
      case 'Light Roast':
        return 'bg-amber-300 text-amber-900';
      case 'Single Origin':
        return 'bg-army-600 text-white';
      case 'Signature Blend':
        return 'bg-army-700 text-white';
      default:
        return 'bg-army-600 text-white';
    }
  };

  const badgeType = getBadgeType();
  const badgeColorClass = getBadgeColorClass(badgeType);

  if (viewMode === 'list') {
    return (
      <div className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-primary-100 hover:border-primary-200">
        <div className="flex">
          {/* Image */}
          <div className="w-48 h-48 flex-shrink-0 relative overflow-hidden">
            <div className={`absolute top-3 left-3 z-10 px-2 py-1 text-xs font-medium rounded-full ${badgeColorClass}`}>
              {badgeType}
            </div>

            <Link to={variantUrl} prefetch="intent" className="block h-full">
              {image && (
                <Image
                  alt={image.altText || product.title}
                  data={image}
                  loading={loading}
                  sizes="192px"
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              )}
              {!image && (
                <div className="w-full h-full bg-tan-100 flex items-center justify-center">
                  <svg className="w-12 h-12 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              )}
            </Link>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 flex flex-col justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-army-600 transition-colors">
                {product.title}
              </h3>

              {/* Origin info */}
              <div className="mb-3">
                <span className="inline-flex items-center text-sm text-army-600">
                  <svg className="w-4 h-4 mr-1.5 text-army-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Origin: Nicaragua
                </span>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl font-bold text-army-600">
                  <Money data={product.priceRange.minVariantPrice} />
                </span>
                {'maxVariantPrice' in product.priceRange && product.priceRange.minVariantPrice.amount !== product.priceRange.maxVariantPrice.amount && (
                  <span className="text-sm text-army-500">
                    - <Money data={product.priceRange.maxVariantPrice} />
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between pt-4 border-t border-army-100">
              <button
                onClick={() => window.location.href = variantUrl}
                className="text-sm font-medium text-army-600 hover:text-army-700 transition-colors flex items-center"
              >
                View Details
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </button>

              {firstVariant ? (
                <AddToCartButton
                  disabled={!('availableForSale' in product ? product.availableForSale : true) || !firstVariant?.availableForSale}
                  lines={[
                    {
                      merchandiseId: firstVariant.id,
                      quantity: 1,
                    },
                  ]}
                  className="bg-army-600 text-white px-4 py-2 rounded-lg hover:bg-army-700 transition-colors duration-300 flex items-center text-sm font-medium disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed disabled:hover:bg-gray-300"
                >
                  <svg width="16" height="16" className="mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
                  </svg>
                  {!('availableForSale' in product ? product.availableForSale : true) || !firstVariant?.availableForSale ? 'Sold Out' : 'Add to Cart'}
                </AddToCartButton>
              ) : (
                <button
                  disabled
                  className="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed text-sm font-medium"
                >
                  Unavailable
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform md:hover:-translate-y-1 overflow-hidden border border-primary-100 hover:border-primary-200">
      {/* Product badge and image */}
      <div className="relative">
        <div className={`absolute top-2 left-2 md:top-3 md:left-3 z-10 px-2 py-1 md:px-3 md:py-1 text-xs font-medium rounded-full ${badgeColorClass}`}>
          {badgeType}
        </div>

        {/* New badge - could be conditionally shown */}
        {product.title.toLowerCase().includes('new') && (
          <div className="absolute top-2 right-2 md:top-3 md:right-3 z-10 px-2 py-1 md:px-3 md:py-1 text-xs font-medium rounded-full bg-yellow-500 text-white">
            New
          </div>
        )}

        {/* Product image */}
        <Link to={variantUrl} prefetch="intent" className="block overflow-hidden">
          <div className="aspect-square bg-tan-100 relative overflow-hidden">
            {image ? (
              <Image
                alt={image.altText || product.title}
                data={image}
                loading={loading}
                sizes="(min-width: 1024px) 25vw, (min-width: 768px) 33vw, 50vw"
                className="w-full h-full object-cover transition-transform duration-700 md:group-hover:scale-110"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-tan-100 text-primary-400">
                <svg className="w-8 h-8 md:w-12 md:h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}

            {/* Quick view overlay - Hidden on mobile for better touch experience */}
            <div className="hidden md:flex absolute inset-0 bg-army-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 items-center justify-center">
              <button
                onClick={() => window.location.href = variantUrl}
                className="bg-army-600 text-white px-4 py-2 rounded-lg font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 hover:bg-army-700"
              >
                View Product
              </button>
            </div>
          </div>
        </Link>
      </div>

      {/* Product info */}
      <div className="p-3 md:p-5">
        <h3 className="text-sm md:text-base font-semibold text-gray-900 mb-2 md:mb-3 group-hover:text-army-600 transition-colors line-clamp-2">
          {product.title}
        </h3>

        {/* Origin badge */}
        <div className="mt-1 mb-2 md:mb-3">
          <span className="inline-flex items-center text-xs text-army-600">
            <svg className="w-3 h-3 mr-1 text-army-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Origin: Nicaragua
          </span>
        </div>

        <div className="flex justify-between items-center pt-2 border-t border-army-100">
          <div className="text-base md:text-lg font-bold text-army-600">
            <Money data={product.priceRange.minVariantPrice} />
          </div>

          <button
            onClick={() => window.location.href = variantUrl}
            className="hidden md:flex text-xs font-medium text-army-600 hover:text-army-700 items-center transition-colors"
          >
            Details
            <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </button>
        </div>

        {/* Add to Cart Button */}
        <div className="mt-2 md:mt-3">
          {firstVariant ? (
            <AddToCartButton
              disabled={!('availableForSale' in product ? product.availableForSale : true) || !firstVariant?.availableForSale}
              lines={[
                {
                  merchandiseId: firstVariant.id,
                  quantity: 1,
                },
              ]}
              className="w-full bg-army-600 text-white py-2 md:py-2.5 px-3 md:px-4 rounded-lg hover:bg-army-700 transition-colors duration-300 flex items-center justify-center text-xs md:text-sm font-medium disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed disabled:hover:bg-gray-300 min-h-[44px]"
            >
              <svg width="14" height="14" className="mr-1 md:mr-2 md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
              </svg>
              {!('availableForSale' in product ? product.availableForSale : true) || !firstVariant?.availableForSale ? 'Sold Out' : 'Add to Cart'}
            </AddToCartButton>
          ) : (
            <button
              disabled
              className="w-full bg-gray-300 text-gray-500 py-2 md:py-2.5 px-3 md:px-4 rounded-lg cursor-not-allowed text-xs md:text-sm font-medium min-h-[44px]"
            >
              Unavailable
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
