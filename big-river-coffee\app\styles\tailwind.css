/**
 * Big River Coffee - Tailwind v4 Configuration
 * Outdoor Adventure Theme with Modern UI
 */

@import 'tailwindcss';

@theme {
  /* Clean Brand Colors - Forest/Army Green Focus */
  --color-forest-50: oklch(0.98 0.01 140);
  --color-forest-100: oklch(0.95 0.02 140);
  --color-forest-200: oklch(0.90 0.04 140);
  --color-forest-300: oklch(0.82 0.06 140);
  --color-forest-400: oklch(0.70 0.08 140);
  --color-forest-500: oklch(0.55 0.10 140);
  --color-forest-600: oklch(0.45 0.12 140);
  --color-forest-700: oklch(0.35 0.10 140);
  --color-forest-800: oklch(0.25 0.08 140);
  --color-forest-900: oklch(0.15 0.06 140);
  --color-forest-950: oklch(0.08 0.04 140);

  /* Army Green - Primary Accent */
  --color-army-50: oklch(0.97 0.01 135);
  --color-army-100: oklch(0.93 0.02 135);
  --color-army-200: oklch(0.86 0.04 135);
  --color-army-300: oklch(0.76 0.06 135);
  --color-army-400: oklch(0.64 0.08 135);
  --color-army-500: oklch(0.52 0.10 135);
  --color-army-600: oklch(0.42 0.12 135);
  --color-army-700: oklch(0.32 0.10 135);
  --color-army-800: oklch(0.22 0.08 135);
  --color-army-900: oklch(0.12 0.06 135);
  --color-army-950: oklch(0.06 0.04 135);

  /* Amber/Orange - Secondary Accent */
  --color-amber-50: oklch(0.98 0.02 85);
  --color-amber-100: oklch(0.95 0.05 85);
  --color-amber-200: oklch(0.90 0.10 85);
  --color-amber-300: oklch(0.84 0.15 85);
  --color-amber-400: oklch(0.76 0.20 85);
  --color-amber-500: oklch(0.68 0.25 85);
  --color-amber-600: oklch(0.58 0.25 85);
  --color-amber-700: oklch(0.48 0.20 85);
  --color-amber-800: oklch(0.38 0.15 85);
  --color-amber-900: oklch(0.28 0.10 85);
  --color-amber-950: oklch(0.18 0.08 85);

  /* Neutral Grays */
  --color-neutral-50: oklch(0.98 0 0);
  --color-neutral-100: oklch(0.95 0 0);
  --color-neutral-200: oklch(0.90 0 0);
  --color-neutral-300: oklch(0.83 0 0);
  --color-neutral-400: oklch(0.64 0 0);
  --color-neutral-500: oklch(0.50 0 0);
  --color-neutral-600: oklch(0.42 0 0);
  --color-neutral-700: oklch(0.32 0 0);
  --color-neutral-800: oklch(0.22 0 0);
  --color-neutral-900: oklch(0.13 0 0);
  --color-neutral-950: oklch(0.07 0 0);

  /* Typography */
  --font-display: "Inter", system-ui, sans-serif;
  --font-body: "Inter", system-ui, sans-serif;
  --font-mono: "JetBrains Mono", monospace;

  /* Spacing Scale */
  --spacing: 0.25rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-adventure: 0 4px 6px -1px oklch(0.15 0.12 142 / 0.1), 0 2px 4px -1px oklch(0.15 0.12 142 / 0.06);
  --shadow-mountain: 0 10px 15px -3px oklch(0.12 0.12 220 / 0.1), 0 4px 6px -2px oklch(0.12 0.12 220 / 0.05);
  --shadow-coffee: 0 20px 25px -5px oklch(0.10 0.08 45 / 0.1), 0 10px 10px -5px oklch(0.10 0.08 45 / 0.04);

  /* Animation Easings */
  --ease-adventure: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-mountain: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-coffee: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Breakpoints */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Clean Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-8 py-3 text-sm font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    background: var(--color-army-600);
    color: white !important;
    border: 1px solid var(--color-army-600);
  }

  .btn-primary:hover {
    background: var(--color-army-700);
    border-color: var(--color-army-700);
    color: white !important;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-8 py-3 text-sm font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    background: transparent;
    color: white !important;
    border: 2px solid var(--color-army-600);
  }

  .btn-secondary:hover {
    background: var(--color-army-600);
    color: white !important;
  }

  .btn-large {
    @apply px-10 py-4 text-base;
  }

  .hero-gradient {
    background: linear-gradient(135deg, var(--color-forest-900) 0%, var(--color-army-900) 100%);
  }

  .card-clean {
    @apply bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden transition-all duration-200;
  }

  .card-clean:hover {
    @apply shadow-md;
    border-color: var(--color-army-200);
  }

  .text-gradient-primary {
    background: linear-gradient(135deg, var(--color-army-600), var(--color-forest-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-clean {
    @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom Animations */
@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
    opacity: 0;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
    opacity: 0;
    transform: scale(0.9);
  }

  .animate-page-enter {
    animation: pageEnter 0.6s ease-out forwards;
    opacity: 0;
  }

  .scale-102 {
    transform: scale(1.02);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Global Styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  line-height: 1.6;
}

.scroll-mt-32 {
  scroll-margin-top: 8rem;
}

.svg-adventure {
  filter: drop-shadow(0 4px 6px oklch(0.15 0.12 142 / 0.1));
}

.svg-mountain {
  filter: drop-shadow(0 4px 6px oklch(0.12 0.12 220 / 0.1));
}
