/* Figma Project: big river (https://www.figma.com/design/WYbvj4CpZ4WbggqZFfWg5y/big-river?node-id=1-2&m=dev)
   This CSS is generated to match the Figma design as closely as possible. */

body, html {
  background: #fff;
  width: 1920px;
  height: 1024px;
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
}

#node-1_2 {
  position: relative;
  width: 1920px;
  height: 1024px;
  background: #fff;
}

#node-1_3 {
  position: absolute;
  left: 0;
  top: 0;
  width: 1920px;
  height: 1080px;
  background: url('homescreenherovid1.png') center/cover no-repeat;
}

#node-1_6 {
  position: absolute;
  left: 558px;
  top: 145px;
  width: 804px;
  height: 366px;
  background: url('Big_River_Coffee_Rectangle_Logo_2_1.png') center/cover no-repeat;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.25);
  border: 1px solid #000;
}

/* Shop Coffee Button */
#node-9_24 {
  position: absolute;
  left: 526px;
  top: 518px;
  width: 343px;
  height: 236px;
}
#node-3_13 {
  position: absolute;
  left: 558.981px;
  top: 518px;
  width: 270.442px;
  height: 228px;
  background: #3a5c5c;
  border: 1px solid #000;
}
#node-8_15 {
  position: absolute;
  left: 571.074px;
  top: 533px;
  width: 245.157px;
  height: 145px;
  background: url('Rectangle.png') center/cover no-repeat;
}
#node-7_2 {
  position: absolute;
  left: 571.074px;
  top: 678px;
  width: 245.157px;
  height: 53px;
  background: #eeedc1;
}
#node-9_18 {
  position: absolute;
  left: 697.5px;
  top: 704.5px;
  width: 343px;
  height: 99px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #000;
  text-align: center;
  line-height: 1em;
  transform: translate(-50%, -50%);
}

/* Learn Our Story Button */
#node-9_25 {
  position: absolute;
  left: 809px;
  top: 518px;
  width: 323px;
  height: 236px;
}
#node-7_8 {
  position: absolute;
  left: 836.952px;
  top: 518px;
  width: 254.673px;
  height: 228px;
  background: #3a5c5c;
  border: 1px solid #000;
}
#node-7_9 {
  position: absolute;
  left: 849.375px;
  top: 678px;
  width: 230.862px;
  height: 53px;
  background: #eeedc1;
}
#node-7_14 {
  position: absolute;
  left: 849.375px;
  top: 533px;
  width: 230.862px;
  height: 142px;
  background: url('man-1869135_1280_1.png') center/cover no-repeat;
}
#node-9_19 {
  position: absolute;
  left: 970.5px;
  top: 704.5px;
  width: 323px;
  height: 99px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #000;
  text-align: center;
  line-height: 1em;
  transform: translate(-50%, -50%);
}

/* Connect With Us Button */
#node-9_26 {
  position: absolute;
  left: 1076px;
  top: 518px;
  width: 332px;
  height: 236px;
}
#node-7_11 {
  position: absolute;
  left: 1100.47px;
  top: 518px;
  width: 261.769px;
  height: 228px;
  background: #3a5c5c;
  border: 1px solid #000;
}
#node-7_12 {
  position: absolute;
  left: 1114.31px;
  top: 678px;
  width: 237.295px;
  height: 53px;
  background: #eeedc1;
}
#node-9_16 {
  position: absolute;
  left: 1114.31px;
  top: 533px;
  width: 237.295px;
  height: 145px;
  background: url('social-media-1795578_1280_1.png') center/cover no-repeat;
}
#node-9_21 {
  position: absolute;
  left: 1242px;
  top: 704.5px;
  width: 332px;
  height: 99px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #000;
  text-align: center;
  line-height: 1em;
  transform: translate(-50%, -50%);
}

/* Mel Blount Banner */
#node-37_46 {
  position: absolute;
  left: 558px;
  top: 754px;
  width: 804px;
  height: 102px;
}
#node-37_32 {
  position: absolute;
  left: 558px;
  top: 754px;
  width: 804px;
  height: 102px;
  background: #282424;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
#node-37_33 {
  position: absolute;
  left: 569.63px;
  top: 766px;
  width: 784.617px;
  height: 78px;
  background: #f2dd00;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}
#node-37_40 {
  position: absolute;
  left: 616.924px;
  top: 806px;
  width: 77.531px;
  height: 82px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 700;
  color: #000;
  text-align: center;
  line-height: 1em;
  text-shadow: 0px 4px 4px rgba(0,0,0,0);
  transform: translate(-50%, -50%);
}
#node-37_47 {
  position: absolute;
  left: 776px;
  top: 761px;
  width: 372.15px;
  height: 83px;
}
#node-37_48 {
  position: absolute;
  left: 776px;
  top: 761px;
  width: 372.15px;
  height: 83px;
}

/* Utility classes for images */
img, .bg-img {
  display: block;
  max-width: none;
  width: 100%;
  height: 100%;
} 