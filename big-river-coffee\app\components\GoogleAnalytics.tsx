import {useAnalytics} from '@shopify/hydrogen';
import {useEffect, useState} from 'react';
import {useUTMTracking} from '~/hooks/useUTMTracking';
import {formatUTMForAnalytics} from '~/lib/utm';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

/**
 * Google Analytics 4 integration component for Big River Coffee
 * This component loads GA4 post-hydration and subscribes to Shopify Analytics events
 */
export function GoogleAnalytics() {
  const {subscribe, register, ready} = useAnalytics();
  const [isGALoaded, setIsGALoaded] = useState(false);
  const {utmParams, hasUTM} = useUTMTracking();

  useEffect(() => {
    // Load Google Analytics script post-hydration to avoid performance issues
    const loadGoogleAnalytics = () => {
      // Check if already loaded
      if (window.gtag) {
        setIsGALoaded(true);
        return;
      }

      // Create and load the GA script
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-KWTBMRWDGP';

      script.onload = () => {
        // Initialize dataLayer and gtag function
        window.dataLayer = window.dataLayer || [];
        function gtag(...args: any[]) {
          window.dataLayer.push(args);
        }
        window.gtag = gtag;

        // Configure Google Analytics
        gtag('js', new Date());
        gtag('config', 'G-KWTBMRWDGP', {
          // Essential for e-commerce attribution
          send_page_view: true,
          allow_google_signals: true,
          allow_ad_personalization_signals: true,
          // Enhanced e-commerce settings
          custom_map: {
            'custom_parameter_1': 'coffee_type',
            'custom_parameter_2': 'subscription_type'
          }
        });

        setIsGALoaded(true);
        console.log('Google Analytics loaded successfully for Big River Coffee');
      };

      script.onerror = () => {
        console.error('Failed to load Google Analytics');
      };

      document.head.appendChild(script);
    };

    // Load GA after a short delay to ensure React hydration is complete
    const timer = setTimeout(loadGoogleAnalytics, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Only register and subscribe after GA is loaded
    if (!isGALoaded) return;

    // Register this analytics integration
    register('Google Analytics 4');

    // Subscribe to page view events
    subscribe('page_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        // Send page view with UTM attribution data
        const pageViewData: any = {
          page_title: data.pageTitle || document.title,
          page_location: data.url || window.location.href,
          page_path: data.path || window.location.pathname,
        };

        // Add UTM campaign attribution if available
        if (hasUTM) {
          // Send campaign data in GA4 format for proper attribution
          pageViewData.campaign_source = utmParams.utm_source;
          pageViewData.campaign_medium = utmParams.utm_medium;
          pageViewData.campaign_name = utmParams.utm_campaign;
          pageViewData.campaign_content = utmParams.utm_content;
          pageViewData.campaign_term = utmParams.utm_term;
          pageViewData.campaign_id = utmParams.utm_id;

          // Also send as custom parameters for detailed tracking
          pageViewData.traffic_source = utmParams.utm_source;
          pageViewData.traffic_medium = utmParams.utm_medium;
          pageViewData.traffic_campaign = utmParams.utm_campaign;
        }

        window.gtag('event', 'page_view', pageViewData);

        // Log UTM tracking for debugging
        if (hasUTM) {
          console.log('🎯 Traffic Source Tracked:', {
            source: utmParams.utm_source,
            medium: utmParams.utm_medium,
            campaign: utmParams.utm_campaign,
            full_data: pageViewData
          });
        }
      }
    });

    // Subscribe to product view events
    subscribe('product_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'view_item', {
          currency: data.currency || 'USD',
          value: data.price || 0,
          items: [{
            item_id: data.productId,
            item_name: data.productTitle,
            item_category: data.productType || 'Coffee',
            item_variant: data.variantTitle,
            price: data.price || 0,
            quantity: 1
          }]
        });
      }
    });

    // Subscribe to add to cart events
    subscribe('product_added_to_cart', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const eventData: any = {
          currency: data.currency || 'USD',
          value: (data.price || 0) * (data.quantity || 1),
          items: [{
            item_id: data.productId,
            item_name: data.productTitle,
            item_category: data.productType || 'Coffee',
            item_variant: data.variantTitle,
            price: data.price || 0,
            quantity: data.quantity || 1
          }]
        };

        // Add traffic source attribution to cart events
        if (hasUTM) {
          eventData.traffic_source = utmParams.utm_source;
          eventData.traffic_medium = utmParams.utm_medium;
          eventData.traffic_campaign = utmParams.utm_campaign;
        }

        window.gtag('event', 'add_to_cart', eventData);
      }
    });

    // Subscribe to cart view events
    subscribe('cart_viewed', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const items = data.cart?.lines?.map((line: any) => ({
          item_id: line.merchandise?.product?.id,
          item_name: line.merchandise?.product?.title,
          item_category: line.merchandise?.product?.productType || 'Coffee',
          item_variant: line.merchandise?.title,
          price: parseFloat(line.merchandise?.price?.amount || '0'),
          quantity: line.quantity || 1
        })) || [];

        window.gtag('event', 'view_cart', {
          currency: data.cart?.cost?.totalAmount?.currencyCode || 'USD',
          value: parseFloat(data.cart?.cost?.totalAmount?.amount || '0'),
          items: items
        });
      }
    });

    // Subscribe to checkout events
    subscribe('checkout_started', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const items = data.cart?.lines?.map((line: any) => ({
          item_id: line.merchandise?.product?.id,
          item_name: line.merchandise?.product?.title,
          item_category: line.merchandise?.product?.productType || 'Coffee',
          item_variant: line.merchandise?.title,
          price: parseFloat(line.merchandise?.price?.amount || '0'),
          quantity: line.quantity || 1
        })) || [];

        window.gtag('event', 'begin_checkout', {
          currency: data.cart?.cost?.totalAmount?.currencyCode || 'USD',
          value: parseFloat(data.cart?.cost?.totalAmount?.amount || '0'),
          items: items
        });
      }
    });

    // Subscribe to purchase events
    subscribe('purchase', (data) => {
      if (typeof window !== 'undefined' && window.gtag) {
        const items = data.order?.lineItems?.map((line: any) => ({
          item_id: line.variant?.product?.id,
          item_name: line.variant?.product?.title,
          item_category: line.variant?.product?.productType || 'Coffee',
          item_variant: line.variant?.title,
          price: parseFloat(line.variant?.price?.amount || '0'),
          quantity: line.quantity || 1
        })) || [];

        const utmData = hasUTM ? formatUTMForAnalytics(utmParams) : {};

        window.gtag('event', 'purchase', {
          transaction_id: data.order?.id,
          currency: data.order?.totalPrice?.currencyCode || 'USD',
          value: parseFloat(data.order?.totalPrice?.amount || '0'),
          items: items,
          // Custom parameters for coffee business
          coffee_subscription: data.order?.lineItems?.some((line: any) =>
            line.sellingPlan?.id
          ) || false,
          // Include UTM attribution data
          ...utmData,
        });

        // Log purchase with UTM attribution
        if (hasUTM) {
          console.log('Purchase conversion with UTM attribution:', {
            transaction_id: data.order?.id,
            value: parseFloat(data.order?.totalPrice?.amount || '0'),
            utm_data: utmData
          });
        }
      }
    });

  }, [isGALoaded, subscribe, register]);

  return null; // This component doesn't render anything
}
