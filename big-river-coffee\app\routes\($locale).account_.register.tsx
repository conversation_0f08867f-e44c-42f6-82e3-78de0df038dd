import {
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from '@shopify/remix-oxygen';
import {Form, useNavigation, type MetaFunction} from 'react-router';

export const meta: MetaFunction = () => {
  return [{title: 'Register | Big River Coffee'}];
};

export async function loader({context}: LoaderFunctionArgs) {
  try {
    const isLoggedIn = await context.customerAccount.isLoggedIn();
    if (isLoggedIn) {
      return redirect('/account');
    }
  } catch (error) {
    // If auth check fails, just continue to show register page
    console.log('Auth check failed, showing register page');
  }

  return {};
}

export async function action({request, context}: ActionFunctionArgs) {
  // For now, just redirect to the Shopify login page
  // In a real implementation, we would handle the registration process here
  // and then redirect to the login page
  return context.customerAccount.login({
    callbackPath: '/account/authorize',
  });
}

export default function Register() {
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f97316' }}>
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100">
          <div className="p-8">
            <div className="text-center mb-8">
              <span className="text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4">Create Account</span>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Join Big River Coffee</h1>
              <p className="text-gray-600">Create your account to track orders and save favorites</p>
            </div>

            <Form method="post" className="space-y-4">
              <div>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="group relative flex w-full justify-center rounded-md bg-army-600 px-4 py-3.5 text-sm font-semibold text-white hover:bg-army-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                    {isSubmitting ? (
                      <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-white group-hover:text-gray-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                      </svg>
                    )}
                  </span>
                  {isSubmitting ? 'Creating account...' : 'Create account'}
                </button>
              </div>
            </Form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <a href="/account/login" className="font-medium text-army-600 hover:text-army-500">
                  Sign in here
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
