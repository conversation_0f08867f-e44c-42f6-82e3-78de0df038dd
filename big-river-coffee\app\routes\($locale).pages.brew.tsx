import { type MetaFunction } from 'react-router';

export const meta: MetaFunction = () => {
  return [
    { title: 'Brewing Guides | Big River Coffee' },
    { description: 'Learn how to brew the perfect cup with our detailed guides for various brewing methods. From pour-over to French press, we\'ve got you covered.' }
  ];
};

export default function BrewPage() {
  return (
    <div style={{ backgroundColor: '#f97316' }}>
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-army-900 text-white">
        <div className="absolute inset-0 z-0 opacity-40">
          <img
            src="/coffeebean.webp"
            alt="Coffee beans background"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6">The Art of Brewing</h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 sm:mb-8">
              Brewing great coffee is both an art and a science. These guides will help you master various brewing methods to bring out the best flavors in your coffee beans.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative overflow-hidden">
        {/* Background decorative circles */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
        <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
        <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
        <div className="absolute bottom-1/4 left-1/3 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
        <div className="absolute top-3/4 right-1/3 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
          <div className="max-w-none">

          {/* Quick Navigation */}
          <div className="mb-16">
            <div className="bg-army-600 rounded-xl p-6 sm:p-8">
              <div className="text-center mb-6">
                <span className="text-army-600 font-semibold text-sm uppercase tracking-wider bg-white px-4 py-1 rounded-full shadow-sm inline-block mb-4">Brewing Methods</span>
                <h3 className="text-xl sm:text-2xl font-bold text-white">Jump to a Brewing Method</h3>
                <div className="w-24 h-1 bg-white rounded my-4 mx-auto"></div>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-5">
                <a href="#pour-over" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Pour Over</span>
                </a>

                <a href="#french-press" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4h16v7a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 15v5m8-5v5m-4-5v5m-6-16v3m12-3v3" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">French Press</span>
                </a>

                <a href="#aeropress" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 12c-3.5 0-7 1.5-7 4v4h14v-4c0-2.5-3.5-4-7-4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 4v8h4V4m-2 12v4" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">AeroPress</span>
                </a>

                <a href="#espresso" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 11h16m-8-7v14" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7a4 4 0 0 0-4 4v6a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4v-6a4 4 0 0 0-4-4H8Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Espresso</span>
                </a>

                <a href="#cold-brew" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v4m-4 4H4m3 4-2 2m10-2 2 2m-4-6a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 14a5 5 0 0 0 10 0v-4a5 5 0 0 0-10 0v4Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Cold Brew</span>
                </a>

                <a href="#moka-pot" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10v10m12-10v10M4 20h16M8 4h8l2 6H6l2-6Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10h12" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Moka Pot</span>
                </a>
              </div>
            </div>
          </div>

          {/* Pour Over Section */}
          <section id="pour-over" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/pour_over.webp"
                    alt="Pour Over Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0">Pour Over (V60/Chemex)</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Medium Difficulty
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        3-4 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    Pour over brewing highlights the delicate flavors and aromas in your coffee. It's perfect for single-origin beans where you want to taste the distinct characteristics.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>V60 or Chemex brewer</li>
                      <li>Paper filter</li>
                      <li>Gooseneck kettle</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>22g coffee (medium-fine grind)</li>
                      <li>360g water (205°F / 96°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Place the filter in the dripper and rinse with hot water to remove paper taste and preheat the brewer</li>
                      <li>Add ground coffee and level the bed</li>
                      <li>Start timer and pour 50g of water for the bloom, making sure all grounds are saturated</li>
                      <li>After 30 seconds, slowly pour water in circular motions until you reach 150g total</li>
                      <li>Continue pouring in stages until you reach 360g total water</li>
                      <li>Total brew time should be 3-4 minutes</li>
                      <li>Enjoy your perfectly extracted pour over coffee!</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <div className="text-center py-16">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Brew?</h2>
            <p className="text-xl text-white mb-8">Get our premium coffee beans delivered to your door</p>
            <a
              href="/collections/all"
              className="inline-flex items-center px-8 py-4 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors duration-200"
              style={{ color: 'white' }}
            >
              <span className="text-white">Try Our Coffee</span>
              <svg className="w-5 h-5 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>

          {/* Shipping Information Banner */}
          <div className="bg-white border-t border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
                  <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-army-600 mb-2">Free Shipping</h3>
                  <p className="text-sm text-army-500">On orders over $30</p>
                </div>
                <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
                  <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-army-600 mb-2">Fast Delivery</h3>
                  <p className="text-sm text-army-500">Ships within 1-2 business days</p>
                </div>
                <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
                  <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-army-600 mb-2">Easy Returns</h3>
                  <p className="text-sm text-army-500">30-day satisfaction guarantee</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
