import { type MetaFunction } from 'react-router';

export const meta: MetaFunction = () => {
  return [
    { title: 'Brewing Guides | Big River Coffee' },
    { description: 'Learn how to brew the perfect cup with our detailed guides for various brewing methods. From pour-over to French press, we\'ve got you covered.' }
  ];
};

export default function BrewPage() {
  return (
    <div style={{ backgroundColor: '#f97316' }}>
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-army-900 text-white">
        <div className="absolute inset-0 z-0 opacity-40">
          <img
            src="/coffeebean.webp"
            alt="Coffee beans background"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6">The Art of Brewing</h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 sm:mb-8">
              Brewing great coffee is both an art and a science. These guides will help you master various brewing methods to bring out the best flavors in your coffee beans.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-none">

          {/* Quick Navigation */}
          <div className="mb-16">
            <div className="bg-white rounded-xl shadow-md p-6 sm:p-8 border border-gray-100">
              <div className="text-center mb-6">
                <span className="text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4">Brewing Methods</span>
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900">Jump to a Brewing Method</h3>
                <div className="w-24 h-1 bg-army-600 rounded my-4 mx-auto"></div>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-5">
                <a href="#pour-over" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">Pour Over</span>
                </a>

                <a href="#french-press" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4h16v7a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 15v5m8-5v5m-4-5v5m-6-16v3m12-3v3" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">French Press</span>
                </a>

                <a href="#aeropress" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 12c-3.5 0-7 1.5-7 4v4h14v-4c0-2.5-3.5-4-7-4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 4v8h4V4m-2 12v4" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">AeroPress</span>
                </a>

                <a href="#espresso" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 11h16m-8-7v14" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7a4 4 0 0 0-4 4v6a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4v-6a4 4 0 0 0-4-4H8Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">Espresso</span>
                </a>

                <a href="#cold-brew" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v4m-4 4H4m3 4-2 2m10-2 2 2m-4-6a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 14a5 5 0 0 0 10 0v-4a5 5 0 0 0-10 0v4Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">Cold Brew</span>
                </a>

                <a href="#moka-pot" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-army-100 flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10v10m12-10v10M4 20h16M8 4h8l2 6H6l2-6Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10h12" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">Moka Pot</span>
                </a>
              </div>
            </div>
          </div>

          {/* Pour Over Section */}
          <section id="pour-over" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/pour_over.webp"
                    alt="Pour Over Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">Pour Over (V60/Chemex)</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Medium Difficulty
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        3-4 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    Pour over brewing highlights the delicate flavors and aromas in your coffee. It's perfect for single-origin beans where you want to taste the distinct characteristics.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>V60 or Chemex brewer</li>
                      <li>Paper filter</li>
                      <li>Gooseneck kettle</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>22g coffee (medium-fine grind)</li>
                      <li>360g water (205°F / 96°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Place the filter in the dripper and rinse with hot water to remove paper taste and preheat the brewer</li>
                      <li>Add ground coffee and level the bed</li>
                      <li>Start timer and pour 50g of water for the bloom, making sure all grounds are saturated</li>
                      <li>After 30 seconds, slowly pour water in circular motions until you reach 150g total</li>
                      <li>Continue pouring in stages (adding 50-70g at a time) until you reach 360g total</li>
                      <li>Allow all water to drain through (total brew time should be 3-4 minutes)</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Pour in slow, steady spirals from the center outward</li>
                      <li>Adjust grind size to control flow rate - finer for slower, coarser for faster</li>
                      <li>For Chemex, use a slightly coarser grind than V60</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* French Press Section */}
          <section id="french-press" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/french_press.webp"
                    alt="French Press Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">French Press</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        4 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    The French Press produces a full-bodied, rich cup with excellent mouthfeel. It's perfect for those who enjoy a robust, flavorful coffee with more oils and fine particles.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>French Press</li>
                      <li>Digital scale</li>
                      <li>Kettle</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                      <li>Wooden spoon or paddle</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>30g coffee (coarse grind)</li>
                      <li>500g water (205°F / 96°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Preheat the French Press with hot water, then discard the water</li>
                      <li>Add ground coffee to the French Press</li>
                      <li>Start timer and add half the water (250g), making sure all grounds are saturated</li>
                      <li>Stir gently with a wooden spoon to break the crust</li>
                      <li>Add remaining water (250g) and place the lid on top with the plunger pulled up</li>
                      <li>After 4 minutes, slowly press the plunger down</li>
                      <li>Pour immediately to prevent over-extraction</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Use a coarse grind to prevent sediment and bitterness</li>
                      <li>Pour immediately after pressing to prevent over-extraction</li>
                      <li>For a cleaner cup, try James Hoffmann's technique: break the crust at 4 minutes, scoop off the foam, wait 5-7 minutes, then press just enough to hold grounds down</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* AeroPress Section */}
          <section id="aeropress" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-xl border border-gray-100">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/aeropress.webp"
                    alt="AeroPress Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <span className="text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4">Brewing Method</span>
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">AeroPress</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        1-2 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    The AeroPress is versatile, portable, and produces a clean, flavorful cup. It combines immersion and pressure brewing for a unique extraction that can be customized to your preferences.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>AeroPress and paper filter</li>
                      <li>Digital scale</li>
                      <li>Kettle</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                      <li>Stirrer (the one included or a spoon)</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>17g coffee (medium-fine grind)</li>
                      <li>250g water (185-205°F / 85-96°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions (Standard Method):</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Place a paper filter in the cap and rinse with hot water</li>
                      <li>Attach the cap to the AeroPress chamber and place on your mug</li>
                      <li>Add coffee to the chamber</li>
                      <li>Start timer and add water (250g)</li>
                      <li>Stir for 10 seconds</li>
                      <li>Insert the plunger and press gently until you hear a hissing sound (should take about 30 seconds)</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Try the inverted method for longer steeping time</li>
                      <li>Experiment with water temperature - lower for lighter roasts, higher for darker roasts</li>
                      <li>Use two paper filters for an even cleaner cup</li>
                      <li>Add a small amount of water after pressing for an Americano-style coffee</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Espresso Section */}
          <section id="espresso" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/brewing_3.webp"
                    alt="Espresso Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">Espresso</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Advanced
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        25-30 sec brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    Espresso is the foundation of many coffee drinks and requires precision. This concentrated coffee showcases intense flavors and creates the beloved crema on top.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>Espresso machine</li>
                      <li>Espresso grinder</li>
                      <li>Tamper</li>
                      <li>Scale</li>
                      <li>Freshly roasted coffee beans (espresso roast recommended)</li>
                      <li>Timer</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>18-20g coffee (fine grind)</li>
                      <li>36-40g water output (1:2 ratio)</li>
                      <li>Water at 200°F / 93°C</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Preheat your machine, portafilter, and cup</li>
                      <li>Grind coffee into the portafilter basket</li>
                      <li>Distribute the grounds evenly</li>
                      <li>Tamp with about 30 pounds of pressure, keeping the tamper level</li>
                      <li>Lock the portafilter into the group head</li>
                      <li>Start the shot and timer simultaneously</li>
                      <li>Aim for a 25-30 second extraction time for a 1:2 ratio (18g in, 36g out)</li>
                      <li>Stop the shot when you reach your target weight</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Adjust grind size to control flow - finer for slower, coarser for faster</li>
                      <li>Look for a honey-like flow that starts after 5-7 seconds</li>
                      <li>The shot should have a caramel-colored crema on top</li>
                      <li>If the shot runs too fast (under 20 seconds), grind finer</li>
                      <li>If the shot runs too slow (over 35 seconds), grind coarser</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Cold Brew Section */}
          <section id="cold-brew" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/cold_brew.webp"
                    alt="Cold Brew Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">Cold Brew</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        12-24 hour brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    Cold brew produces a smooth, low-acid coffee concentrate that can be enjoyed cold or hot. The slow extraction brings out sweet, chocolatey notes while minimizing bitterness.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>Large jar or cold brew maker</li>
                      <li>Filter (cloth, paper, or metal)</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Filtered water</li>
                      <li>Scale</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>100g coffee (coarse grind)</li>
                      <li>800g cold filtered water (1:8 ratio)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Grind coffee to a coarse consistency (similar to sea salt)</li>
                      <li>Add coffee to your container</li>
                      <li>Pour cold filtered water over the grounds</li>
                      <li>Stir gently to ensure all grounds are saturated</li>
                      <li>Cover and let steep at room temperature for 12-24 hours</li>
                      <li>Strain through a filter into a clean container</li>
                      <li>Store in the refrigerator for up to 2 weeks</li>
                      <li>Dilute with water or milk to taste (typically 1:1 ratio)</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Use medium to dark roasts for more pronounced chocolate and caramel notes</li>
                      <li>Double-filter for a cleaner cup</li>
                      <li>Try a hot bloom (small amount of hot water) before adding cold water for more flavor</li>
                      <li>Experiment with steeping time - longer for stronger brew, shorter for milder flavor</li>
                      <li>Make coffee ice cubes to avoid diluting your cold brew</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Moka Pot Section */}
          <section id="moka-pot" className="mb-24">
            <div className="bg-white rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/mokapot_new.webp"
                    alt="Moka Pot Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-0">Moka Pot</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Medium Difficulty
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        4-5 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    The Moka Pot produces a rich, concentrated coffee similar to espresso. This stovetop brewer uses pressure to force water through coffee grounds, creating a bold, full-flavored cup.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>Moka Pot</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Hot water</li>
                      <li>Heat source (stove)</li>
                      <li>Towel or oven mitt</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-gray-600">
                      <li>15-18g coffee (medium-fine grind)</li>
                      <li>Water filled to the valve (about 200ml for a 3-cup pot)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-gray-600">
                      <li>Fill the bottom chamber with hot water up to the valve</li>
                      <li>Insert the funnel filter and fill with coffee (don't tamp)</li>
                      <li>Brush away any coffee grounds from the rim</li>
                      <li>Screw on the top chamber tightly</li>
                      <li>Place on medium-low heat</li>
                      <li>Keep the lid open to observe the brewing process</li>
                      <li>When coffee begins to flow, reduce heat to low</li>
                      <li>Remove from heat when you hear a gurgling sound or when the stream turns blonde</li>
                      <li>Wrap the bottom with a cold, damp towel to stop extraction (optional)</li>
                      <li>Pour and enjoy immediately</li>
                    </ol>
                  </div>

                  <div className="mt-6 p-5 bg-amber-50 rounded-lg border border-amber-200">
                    <h3 className="text-lg font-medium text-amber-700 mb-3">Pro Tips:</h3>
                    <ul className="list-disc pl-5 text-gray-700">
                      <li>Start with hot water to minimize the grounds being exposed to heat for too long</li>
                      <li>Use medium heat to avoid burning the coffee</li>
                      <li>Remove from heat before the extraction is complete to avoid bitter notes</li>
                      <li>Clean your Moka Pot after each use, but avoid using soap (it can affect flavor)</li>
                      <li>Let the pot cool before disassembling</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Try Our Coffee Button */}
          <div className="text-center mb-24">
            <a
              href="/collections/all"
              className="inline-flex items-center px-8 py-4 rounded-lg font-medium bg-army-600 hover:bg-army-700 transition-all duration-300 shadow-sm"
              style={{ color: 'white' }}
            >
              <span className="text-white">Try Our Coffee</span>
              <svg className="w-5 h-5 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      {/* Shipping Information Banner */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Free Shipping</h3>
              <p className="text-sm text-gray-600">On orders over $30</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Fast Delivery</h3>
              <p className="text-sm text-gray-600">Ships within 1-2 business days</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-12 h-12 bg-army-100 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Easy Returns</h3>
              <p className="text-sm text-gray-600">30-day satisfaction guarantee</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
